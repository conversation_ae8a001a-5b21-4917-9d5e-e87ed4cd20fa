let
    Source = Folder.Files("C:\WorkFileDocuments\PeggingReport"),
    #"Sorted Rows" = Table.Sort(Source,{{"Date modified", Order.Descending}}),
    #"Kept First Rows" = Table.FirstN(#"Sorted Rows",2),
    #"Added Custom" = Table.AddColumn(#"Kept First Rows", "Custom", each Csv.Document([Content])),
    #"Removed Other Columns" = Table.SelectColumns(#"Added Custom",{"Custom"}),
    #"Expanded Custom" = Table.ExpandTableColumn(#"Removed Other Columns", "Custom", {"Column1", "Column2", "Column3", "Column4", "Column5", "Column6", "Column7", "Column8", "Column9", "Column10", "Column11", "Column12", "Column13", "Column14", "Column15", "Column16", "Column17", "Column18", "Column19", "Column20", "Column21", "Column22", "Column23", "Column24", "Column25", "Column26", "Column27", "Column28", "Column29", "Column30", "Column31", "Column32", "Column33", "Column34", "Column35", "Column36", "Column37", "Column38", "Column39", "Column40", "Column41", "Column42", "Column43", "Column44", "Column45", "Column46", "Column47", "Column48", "Column49", "Column50", "Column51", "Column52", "Column53", "Column54"}),
    #"Promoted Headers" = Table.PromoteHeaders(#"Expanded Custom", [PromoteAllScalars=true]),
    #"Removed Columns" = Table.RemoveColumns(#"Promoted Headers",{"MRP Run","Forecasted Product","Embedded Flag", "Global Region", "DISCP Region", "CCN", "Buyer Name", "Buyer ID", "Demand Source", "Planner ID", "Other Product Info", "Forecast Method", "CFG Name", "MOD", "Part Description", "Issue Code", "DIT Commodity", "DIT Comm Code", "Glovia Comm Code", "Measures", "Quantity Per"}),
    #"Changed Type" = Table.TransformColumnTypes(#"Removed Columns",{{"Location", type text}, {"Part Number", type text}} & List.Transform(List.Skip(Table.ColumnNames(#"Removed Columns"), 2), each {_, type number})),
    #"Unpivoted Other Columns" = Table.UnpivotOtherColumns(#"Changed Type", {"Location", "Part Number"}, "Attribute", "Value"),
    #"Grouped Rows" = Table.Group(#"Unpivoted Other Columns", {"Location", "Part Number", "Attribute"}, {
        {"Total Qty", each List.Sum([Value]), type number}
    }),
    #"Pivoted Column" = Table.Pivot(#"Grouped Rows", List.Distinct(#"Grouped Rows"[Attribute]), "Attribute", "Total Qty"),
    #"Filtered Rows" = Table.SelectRows(#"Pivoted Column", each ([Location] <> "Location" and [Location] <> "WISJUA" and [Location] <> "WISXIN" and [Location] <> "WISZHO")),
    #"Merged Queries" = Table.NestedJoin(#"Filtered Rows", {"Part Number"}, #"GetLatestL3FromHyperLink(Copy To MRP)", {"L3"}, "GetLatestL3FromHyperLink(Copy To MRP) (2)", JoinKind.LeftOuter),
    #"Expanded GetLatestL3FromHyperLink(Copy To MRP) (2)" = Table.ExpandTableColumn(#"Merged Queries", "GetLatestL3FromHyperLink(Copy To MRP) (2)", {"Platform", "Project", "Part Type", "DPN"}, {"Platform.1", "Project.1", "Part Type.1", "DPN"}),
    #"Filtered Rows1" = Table.SelectRows(#"Expanded GetLatestL3FromHyperLink(Copy To MRP) (2)", each ([DPN] <> null)),
    #"Reordered Columns" = Table.ReorderColumns(#"Filtered Rows1", List.FirstN(Table.ColumnNames(#"Filtered Rows1"), 3) & List.Skip(Table.ColumnNames(#"Filtered Rows1"), 3)),
    #"Renamed Columns" = Table.RenameColumns(#"Reordered Columns",{{"Part Number", "L3"}}),
    #"Added Run Rate" = Table.AddColumn(#"Renamed Columns", "Run Rate", each Number.Round(List.Sum(List.FirstN(List.Skip(Record.ToList(_), 4), 4)) / 20, 2)),    // 按Location和DPN分组，保留Run Rate（保留2位小数）
    #"Grouped By Location DPN" = Table.Group(#"Added Run Rate", {"Location", "DPN", "Platform.1", "Project.1", "Part Type.1"}, {
        {"Details", each _, type table},
        {"Run Rate", each Number.Round(List.Sum([Run Rate]), 1), type number}
    }),
      // 先汇总Details中的数值
    #"Aggregated Details" = Table.TransformColumns(#"Grouped By Location DPN", {
        {"Details", each let
            tbl = _,
            columns = Table.ColumnNames(tbl),
            numericColumns = List.Difference(columns, {"Location", "DPN", "Run Rate"}),
            result = Table.Group(tbl, {}, List.Transform(numericColumns, each {_, (t) => List.Sum(Table.Column(t, _)), type number}))
        in
            result
        }
    }),
      // 展开汇总后的Details表格    
      #"Expanded Details" = Table.ExpandTableColumn(#"Aggregated Details", "Details", 
        List.Difference(
            Table.ColumnNames(#"Added Run Rate"), 
            {"Location", "DPN", "L3", "Run Rate", "Platform.1", "Project.1", "Part Type.1"}
        )
    ),    // 获取日期格式的列名并转换为月份
    DateColumns = List.Select(Table.ColumnNames(#"Expanded Details"), each try Date.FromText(_) is date otherwise false),
      // 创建月份映射（英文）
    MonthMapping = List.Transform(
        DateColumns,
        each [
            OriginalColumn = _,
            MonthName = Date.ToText(Date.FromText(_), "MMM", "en-US")
        ]
    ),
    
    // 按月份分组
    #"Added Monthly Totals" = List.Accumulate(
        List.Distinct(List.Transform(MonthMapping, each [MonthName])),
        #"Expanded Details",
        (table, month) =>
            let
                relevantColumns = List.Select(
                    MonthMapping,
                    each [MonthName] = month
                ),
                columnNames = List.Transform(relevantColumns, each [OriginalColumn])
            in
                Table.AddColumn(
                    table,
                    month,
                    each List.Sum(List.Transform(columnNames, (col) => Record.Field(_, col))),
                    type number
                )
    ),    #"Merged Queries1" = Table.NestedJoin(#"Added Monthly Totals", {"DPN"}, Collabs_BuyerName, {"*DGP Item"}, "Collabs_BuyerName", JoinKind.LeftOuter),
    #"Expanded Collabs_BuyerName" = Table.ExpandTableColumn(#"Merged Queries1", "Collabs_BuyerName", {"*EMS Buyer Name"}, {"Buyer Name"}),
    // 将 Backlog 转换为数字类型
    #"Changed Backlog Type" = Table.TransformColumnTypes(#"Expanded Collabs_BuyerName",{{"Backlog", type number}}),    // 添加 TTL 列，包含日期列总和和Backlog
    #"Added TTL" = Table.AddColumn(
        #"Changed Backlog Type",
        "TTL",
        each List.Sum(List.Transform(DateColumns, (col) => Record.Field(_, col))) + (if [Backlog] = null then 0 else [Backlog]),
        type number
    ),
    // 获取所有非日期列和非基础列
    OtherColumns = List.Select(
        Table.ColumnNames(#"Added TTL"),
        each not List.Contains(DateColumns, _) and 
            not List.Contains(
                {"Location", "DPN", "Run Rate", "L3", "Platform.1", "Project.1", 
                 "Part Type.1", "Backlog", "Buyer Name", "TTL"}, 
                _
            )
    ),
    // 重新排序列，整理成最终顺序
    #"Reordered Columns Final" = Table.ReorderColumns(
        #"Added TTL",
        List.Combine({
            // 1. 基础信息列
            {"Platform.1", "Project.1", "DPN", "Part Type.1", "Location", "Run Rate", "Buyer Name"},
            // 2. Backlog列
            {"Backlog"},
            // 3. 月份汇总列
            OtherColumns,
            // 4. TTL总计列
            {"TTL"},
            // 5. 原始日期列
            DateColumns
        })
    ),
    #"Renamed Columns1" = Table.RenameColumns(#"Reordered Columns Final",{{"Platform.1", "Platform"}, {"Project.1", "Project"}, {"Part Type.1", "Part Type"}}),
    #"Filtered Rows2" = Table.SelectRows(#"Renamed Columns1", each ([Buyer Name] <> null)),

    // 添加 Life of Cycle 列
    #"Added Life of Cycle" = Table.AddColumn(
        #"Filtered Rows2",
        "Life of Cycle",
        each let
            // 获取月份汇总列（排除基础列、TTL和原始日期列）
            monthColumns = List.Select(
                Table.ColumnNames(#"Filtered Rows2"),
                each not List.Contains(DateColumns, _) and
                    not List.Contains(
                        {"Platform", "Project", "DPN", "Part Type", "Location", "Run Rate",
                         "Buyer Name", "Backlog", "TTL"},
                        _
                    )
            ),

            // 获取第一个月和第六个月的列名
            firstMonth = List.First(monthColumns),
            sixthMonth = if List.Count(monthColumns) >= 6 then monthColumns{5} else null,

            // 获取第一个月对应的原始日期列
            firstMonthDateColumns = List.Select(
                DateColumns,
                each let
                    dateValue = Date.FromText(_),
                    monthName = Date.ToText(dateValue, "MMM", "en-US")
                in
                    monthName = firstMonth
            ),

            // 按日期排序，取前三周（前21天）的列
            sortedFirstMonthColumns = List.Sort(
                firstMonthDateColumns,
                each Date.FromText(_)
            ),

            // 取前三周的列（假设每周5个工作日，前三周约15个工作日）
            // 但为了更准确，我们取前21天的列
            firstThreeWeeksColumns = List.Select(
                sortedFirstMonthColumns,
                each let
                    dateValue = Date.FromText(_),
                    firstDate = Date.FromText(List.First(sortedFirstMonthColumns)),
                    daysDiff = Duration.Days(dateValue - firstDate)
                in
                    daysDiff <= 20  // 前21天（约3周）
            ),

            // 计算前三周总和
            firstThreeWeeksSum = List.Sum(
                List.Transform(
                    firstThreeWeeksColumns,
                    (col) => if Record.HasFields(_, col) then Record.Field(_, col) else 0
                )
            ),

            // 获取第六个月的值
            sixthMonthValue = if sixthMonth <> null and Record.HasFields(_, sixthMonth)
                             then Record.Field(_, sixthMonth)
                             else 0,

            // 应用逻辑：前三周=0则EOL，否则第六个月>0则MP，否则LTB
            result = if firstThreeWeeksSum = 0
                    then "EOL"
                    else if sixthMonthValue > 0
                         then "MP"
                         else "LTB"
        in
            result,
        type text
    )
in
    #"Added Life of Cycle"